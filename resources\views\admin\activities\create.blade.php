@extends('layouts.admin')

@section('title', 'เพิ่มรูปภาพใหม่ - แกลลอรี่')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.activities') }}">แกลลอรี่รูปภาพ</a></li>
<li class="breadcrumb-item active">เพิ่มรูปภาพใหม่</li>
@endsection

@section('content')
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพใหม่
                </h1>
                <p class="text-muted mb-0">อัพโหลดรูปภาพใหม่ไปยังแกลลอรี่</p>
            </div>
            <a href="{{ route('admin.activities') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>กลับไปแกลลอรี่
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.activities.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ <span class="text-danger">*</span></label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" name="image" accept="image/*" required>
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB</div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อเรื่อง <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror"
                               id="title" name="title" value="{{ old('title') }}" required
                               placeholder="เช่น ภาพบรรยากาศงานเปิดตัวผลิตภัณฑ์">
                        @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="3" required
                                  placeholder="เขียนคำอธิบายเกี่ยวกับรูปภาพนี้...">{{ old('description') }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Hidden fields for compatibility -->
                    <input type="hidden" name="activity_date" value="{{ now()->format('Y-m-d') }}">
                    <input type="hidden" name="location" value="">
                    <input type="hidden" name="details" value="">

                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0"
                               placeholder="0 = แสดงก่อน, ตัวเลขมาก = แสดงหลัง">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">ใช้สำหรับจัดเรียงลำดับการแสดงในแกลลอรี่</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-eye me-1"></i>เผยแพร่รูปภาพนี้ในแกลลอรี่
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก รูปภาพจะถูกบันทึกแต่ไม่แสดงในหน้าเว็บไซต์</div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>อัพโหลดรูปภาพ
                        </button>
                        <a href="{{ route('admin.activities') }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-camera text-primary me-2"></i>คำแนะนำการอัพโหลด
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        ใช้รูปภาพที่มีความละเอียดสูง
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        ตั้งชื่อเรื่องที่สื่อความหมาย
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        เขียนคำอธิบายที่น่าสนใจ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        ขนาดไฟล์ไม่เกิน 2MB
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        รองรับไฟล์ JPG, PNG, GIF
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        ตั้งลำดับการแสดงตามต้องการ
                    </li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images text-info me-2"></i>ตัวอย่างรูปภาพ
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-1">📸 ภาพบรรยากาศงานต่างๆ</li>
                    <li class="mb-1">🎉 ภาพงานเปิดตัวผลิตภัณฑ์</li>
                    <li class="mb-1">👥 ภาพทีมงานและลูกค้า</li>
                    <li class="mb-1">🏢 ภาพสำนักงานและสถานที่</li>
                    <li class="mb-1">🎯 ภาพผลงานและโปรเจค</li>
                    <li>✨ ภาพกิจกรรมพิเศษต่างๆ</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];

        if (file) {
            // Check file size (2MB = 2 * 1024 * 1024 bytes)
            if (file.size > 2 * 1024 * 1024) {
                alert('ขนาดไฟล์ใหญ่เกินไป กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 2MB');
                imageInput.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('ประเภทไฟล์ไม่ถูกต้อง กรุณาเลือกไฟล์ JPG, PNG หรือ GIF');
                imageInput.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
        }
    });

    // Auto-generate title from filename
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        const titleInput = document.getElementById('title');

        if (file && !titleInput.value) {
            // Remove file extension and format filename
            let filename = file.name.replace(/\.[^/.]+$/, "");
            filename = filename.replace(/[-_]/g, ' ');
            filename = filename.charAt(0).toUpperCase() + filename.slice(1);
            titleInput.value = filename;
        }
    });
});
</script>
@endsection
