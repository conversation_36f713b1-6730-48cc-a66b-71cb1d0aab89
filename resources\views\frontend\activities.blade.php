@extends('layouts.app')

@section('title', 'กิจกรรม - ' . ($settings['site_name'] ?? 'บริษัทของเรา'))

@section('content')
<!-- Page Header -->
<section class="hero-section">
    <div class="container">
        <div class="text-center">
            <h1 class="display-4 fw-bold mb-4">กิจกรรมของเรา</h1>
            <p class="lead">ติดตามกิจกรรมและผลงานที่เราได้ทำร่วมกับลูกค้า</p>
        </div>
    </div>
</section>

<!-- Activities Section -->
<section class="py-5">
    <div class="container">
        @if($activities->count() > 0)
        <div class="row g-4">
            @foreach($activities as $activity)
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    <img src="{{ asset('storage/' . $activity->image) }}" class="card-img-top" alt="{{ $activity->title }}" style="height: 250px; object-fit: cover;">
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $activity->title }}</h5>
                        <p class="card-text flex-grow-1">{{ $activity->description }}</p>
                        
                        <div class="activity-meta mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar text-primary me-2"></i>
                                <small class="text-muted">{{ $activity->activity_date->format('d/m/Y') }}</small>
                            </div>
                            @if($activity->location)
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <small class="text-muted">{{ $activity->location }}</small>
                            </div>
                            @endif
                        </div>
                        
                        @if($activity->details)
                        <div class="mt-auto">
                            <button class="btn btn-outline-primary w-100" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#details-{{ $activity->id }}" aria-expanded="false">
                                ดูรายละเอียด
                            </button>
                            <div class="collapse mt-3" id="details-{{ $activity->id }}">
                                <div class="card card-body bg-light">
                                    <p class="small mb-0">{{ $activity->details }}</p>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีกิจกรรม</h3>
            <p class="text-muted">กรุณาติดตามกิจกรรมของเราในอนาคต</p>
            <a href="{{ route('contact') }}" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        @endif
    </div>
</section>

<!-- Gallery Section -->
@if($activities->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">แกลเลอรี่กิจกรรม</h2>
            <p class="text-muted">ภาพบรรยากาศจากกิจกรรมต่างๆ ที่ผ่านมา</p>
        </div>
        
        <div class="row g-3">
            @foreach($activities->take(8) as $activity)
            <div class="col-md-3 col-6">
                <div class="gallery-item">
                    <img src="{{ asset('storage/' . $activity->image) }}" 
                         class="img-fluid rounded shadow-sm" 
                         alt="{{ $activity->title }}"
                         style="height: 200px; width: 100%; object-fit: cover; cursor: pointer;"
                         data-bs-toggle="modal" 
                         data-bs-target="#imageModal"
                         data-image="{{ asset('storage/' . $activity->image) }}"
                         data-title="{{ $activity->title }}"
                         data-description="{{ $activity->description }}"
                         data-date="{{ $activity->activity_date->format('d/m/Y') }}"
                         data-location="{{ $activity->location }}">
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid rounded mb-3" alt="">
                <p id="modalDescription" class="text-muted"></p>
                <div class="d-flex justify-content-center gap-4">
                    <div id="modalDate"></div>
                    <div id="modalLocation"></div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">สนใจร่วมกิจกรรมกับเรา?</h2>
                <p class="lead mb-4">ติดต่อเราเพื่อสอบถามเกี่ยวกับการร่วมงานและกิจกรรมต่างๆ</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">ติดต่อเรา</a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    
    if (imageModal) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const image = button.getAttribute('data-image');
            const title = button.getAttribute('data-title');
            const description = button.getAttribute('data-description');
            const date = button.getAttribute('data-date');
            const location = button.getAttribute('data-location');
            
            document.getElementById('modalImage').src = image;
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalDescription').textContent = description;
            document.getElementById('modalDate').innerHTML = '<i class="fas fa-calendar text-primary me-2"></i>' + date;
            
            if (location) {
                document.getElementById('modalLocation').innerHTML = '<i class="fas fa-map-marker-alt text-primary me-2"></i>' + location;
            } else {
                document.getElementById('modalLocation').innerHTML = '';
            }
        });
    }
});
</script>
@endsection
