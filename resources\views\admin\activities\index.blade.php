@extends('layouts.admin')

@section('title', 'จัดการกิจกรรม - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">จัดการกิจกรรม</li>
@endsection

@section('content')
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-images me-2"></i>แกลลอรี่รูปภาพ
                </h1>
                <p class="text-muted mb-0">จัดการรูปภาพและโพสต์ต่างๆ</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" onclick="toggleView()">
                    <i class="fas fa-th-large me-2" id="viewIcon"></i>
                    <span id="viewText">มุมมองการ์ด</span>
                </button>
                <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card stats-card h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">รูปภาพทั้งหมด</h6>
                        <h3 class="mb-0">{{ $activities->count() }}</h3>
                    </div>
                    <i class="fas fa-images fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--success-color), #059669);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">รูปที่เผยแพร่</h6>
                        <h3 class="mb-0">{{ $activities->where('is_active', 1)->count() }}</h3>
                    </div>
                    <i class="fas fa-check-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--info-color), #0891b2);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">รูปเดือนนี้</h6>
                        <h3 class="mb-0">{{ $activities->where('created_at', '>=', now()->startOfMonth())->where('created_at', '<=', now()->endOfMonth())->count() }}</h3>
                    </div>
                    <i class="fas fa-calendar fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activities Content -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-images me-2"></i>แกลลอรี่รูปภาพ
            </h5>
            <div class="d-flex gap-2">
                <div class="input-group" style="width: 300px;">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" placeholder="ค้นหารูปภาพ..." id="searchInput">
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if($activities->count() > 0)

        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 120px;">รูปภาพ</th>
                            <th>ชื่อเรื่อง</th>
                            <th>คำอธิบาย</th>
                            <th style="width: 120px;">วันที่อัพโหลด</th>
                            <th style="width: 100px;">สถานะ</th>
                            <th style="width: 80px;">ลำดับ</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($activities as $activity)
                        <tr class="activity-row">
                            <td>
                                @if($activity->image)
                                <img src="{{ asset('storage/' . $activity->image) }}" alt="{{ $activity->title }}"
                                     class="img-thumbnail rounded" style="width: 80px; height: 80px; object-fit: cover; cursor: pointer;"
                                     data-bs-toggle="modal" data-bs-target="#imageModal"
                                     data-image="{{ asset('storage/' . $activity->image) }}"
                                     data-title="{{ $activity->title }}"
                                     data-description="{{ $activity->description }}">
                                @else
                                <div class="bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center rounded"
                                     style="width: 80px; height: 80px;">
                                    <i class="fas fa-images text-muted"></i>
                                </div>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <strong class="activity-title">{{ $activity->title }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>
                                        คลิกเพื่อดูรูปใหญ่
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="activity-description">{{ Str::limit($activity->description, 80) }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $activity->created_at->format('d/m/Y H:i') }}</span>
                            </td>
                            <td>
                                @if($activity->is_active)
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>เปิดใช้
                                </span>
                                @else
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>ปิดใช้
                                </span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $activity->sort_order ?? 0 }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.activities.edit', $activity->id) }}"
                                       class="btn btn-sm btn-outline-primary" title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.activities.delete', $activity->id) }}"
                                          method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้?')" title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Gallery View -->
        <div id="cardView" style="display: none;">
            <div class="row g-3">
                @foreach($activities as $activity)
                <div class="col-md-3 col-sm-4 col-6 activity-card">
                    <div class="card h-100 interactive-card gallery-item">
                        @if($activity->image)
                        <div class="position-relative">
                            <img src="{{ asset('storage/' . $activity->image) }}" class="card-img-top"
                                 style="height: 250px; object-fit: cover; cursor: pointer;" alt="{{ $activity->title }}"
                                 data-bs-toggle="modal" data-bs-target="#imageModal"
                                 data-image="{{ asset('storage/' . $activity->image) }}"
                                 data-title="{{ $activity->title }}"
                                 data-description="{{ $activity->description }}">

                            <!-- Overlay with status -->
                            <div class="position-absolute top-0 end-0 m-2">
                                @if($activity->is_active)
                                <span class="badge bg-success bg-opacity-90">
                                    <i class="fas fa-eye me-1"></i>เผยแพร่
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-90">
                                    <i class="fas fa-eye-slash me-1"></i>ซ่อน
                                </span>
                                @endif
                            </div>

                            <!-- Hover overlay -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50 opacity-0 hover-overlay" style="transition: opacity 0.3s ease;">
                                <i class="fas fa-search-plus fa-2x text-white"></i>
                            </div>
                        </div>
                        @else
                        <div class="card-img-top bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center"
                             style="height: 250px;">
                            <i class="fas fa-images fa-3x text-muted"></i>
                        </div>
                        @endif

                        <div class="card-body p-3">
                            <h6 class="card-title activity-title mb-2">{{ Str::limit($activity->title, 30) }}</h6>
                            <p class="card-text activity-description small text-muted mb-2">{{ Str::limit($activity->description, 60) }}</p>

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>{{ $activity->created_at->format('d/m/Y') }}
                                </small>
                                <small class="text-muted">#{{ $activity->sort_order ?? 0 }}</small>
                            </div>
                        </div>

                        <div class="card-footer bg-transparent p-2">
                            <div class="d-flex gap-1">
                                <a href="{{ route('admin.activities.edit', $activity->id) }}"
                                   class="btn btn-sm btn-primary flex-fill">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.activities.delete', $activity->id) }}"
                                      method="POST" class="flex-fill">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger w-100"
                                            onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h4 class="text-muted mb-2">ยังไม่มีรูปภาพ</h4>
            <p class="text-muted mb-4">เริ่มต้นด้วยการเพิ่มรูปภาพแรกของคุณ</p>
            <a href="{{ route('admin.activities.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพใหม่
            </a>
        </div>
        @endif
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">รูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid rounded mb-3" style="max-height: 500px;">
                <h6 id="modalTitle" class="mb-2"></h6>
                <p id="modalDescription" class="text-muted"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentView = 'table';

function toggleView() {
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const viewIcon = document.getElementById('viewIcon');
    const viewText = document.getElementById('viewText');

    if (currentView === 'table') {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
        viewIcon.className = 'fas fa-list me-2';
        viewText.textContent = 'มุมมองตาราง';
        currentView = 'card';
    } else {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
        viewIcon.className = 'fas fa-th-large me-2';
        viewText.textContent = 'มุมมองการ์ด';
        currentView = 'table';
    }
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const activityRows = document.querySelectorAll('.activity-row');
    const activityCards = document.querySelectorAll('.activity-card');

    // Search in table view
    activityRows.forEach(row => {
        const title = row.querySelector('.activity-title').textContent.toLowerCase();
        const description = row.querySelector('.activity-description').textContent.toLowerCase();
        const location = row.querySelector('.activity-location')?.textContent.toLowerCase() || '';

        if (title.includes(searchTerm) || description.includes(searchTerm) || location.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // Search in card view
    activityCards.forEach(card => {
        const title = card.querySelector('.activity-title').textContent.toLowerCase();
        const description = card.querySelector('.activity-description').textContent.toLowerCase();
        const location = card.querySelector('.activity-location')?.textContent.toLowerCase() || '';

        if (title.includes(searchTerm) || description.includes(searchTerm) || location.includes(searchTerm)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
});

// Add animation to cards
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.interactive-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Image modal functionality
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');

    // Handle image clicks
    document.addEventListener('click', function(e) {
        if (e.target.hasAttribute('data-bs-toggle') && e.target.getAttribute('data-bs-target') === '#imageModal') {
            const imageSrc = e.target.getAttribute('data-image');
            const title = e.target.getAttribute('data-title');
            const description = e.target.getAttribute('data-description');

            modalImage.src = imageSrc;
            modalImage.alt = title;
            modalTitle.textContent = title;
            modalDescription.textContent = description;
        }
    });

    // Gallery hover effects
    const galleryItems = document.querySelectorAll('.gallery-item');
    galleryItems.forEach(item => {
        const overlay = item.querySelector('.hover-overlay');
        if (overlay) {
            item.addEventListener('mouseenter', function() {
                overlay.style.opacity = '1';
            });

            item.addEventListener('mouseleave', function() {
                overlay.style.opacity = '0';
            });
        }
    });
});
</script>
@endsection
