<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Activity;

class ActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create sample activities
        $activities = [
            [
                'title' => 'งานเปิดตัวเว็บไซต์ใหม่',
                'description' => 'งานเปิดตัวเว็บไซต์รูปแบบใหม่ของบริษัท',
                'details' => 'จัดงานเปิดตัวเว็บไซต์ใหม่ที่มีฟีเจอร์ครบครันและใช้งานง่าย พร้อมการสาธิตการใช้งานและรับฟังความคิดเห็นจากลูกค้า',
                'image' => 'activities/sample1.jpg',
                'activity_date' => now()->subDays(30),
                'location' => 'ห้องประชุมใหญ่ ชั้น 5',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'สัมมนาการตลาดออนไลน์',
                'description' => 'สัมมนาเรื่องกลยุทธ์การตลาดออนไลน์ในยุคดิจิทัล',
                'details' => 'การสัมมนาที่รวบรวมผู้เชี่ยวชาญด้านการตลาดออนไลน์มาแชร์ประสบการณ์และเทคนิคต่างๆ ที่จะช่วยให้ธุรกิจเติบโตในโลกดิจิทัล',
                'image' => 'activities/sample2.jpg',
                'activity_date' => now()->subDays(15),
                'location' => 'โรงแรมแกรนด์ ห้องบอลรูม',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'Workshop การออกแบบ UX/UI',
                'description' => 'เวิร์คช็อปการออกแบบประสบการณ์ผู้ใช้และส่วนติดต่อ',
                'details' => 'เวิร์คช็อปเชิงปฏิบัติการที่สอนหลักการออกแบบ UX/UI ตั้งแต่พื้นฐานจนถึงขั้นสูง พร้อมการฝึกปฏิบัติจริง',
                'image' => 'activities/sample3.jpg',
                'activity_date' => now()->subDays(7),
                'location' => 'ศูนย์การเรียนรู้ดิจิทัล',
                'is_active' => true,
                'sort_order' => 3
            ]
        ];

        foreach ($activities as $activity) {
            Activity::create($activity);
        }
    }
}
